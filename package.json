{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/devtools": "2.6.2", "@nuxt/fonts": "0.11.4", "@nuxt/icon": "1.15.0", "@nuxt/image": "1.11.0", "@nuxt/ui": "3.3.0", "nuxt": "^4.0.1", "typescript": "^5.9.2", "vite": "^7.0.6", "vue": "^3.5.18", "vue-router": "^4.5.1"}}